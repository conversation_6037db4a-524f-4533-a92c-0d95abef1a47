#!/usr/bin/env python3
"""
Simple test script for AgenticSeek functionality
"""

import sys
import os
import asyncio
from sources.llm_provider import Provider

async def test_basic_functionality():
    """Test basic AgenticSeek functionality"""
    print("🚀 Testing AgenticSeek Basic Functionality")
    print("=" * 50)

    # Test 1: Provider initialization
    print("1. Testing DeepSeek Provider...")
    try:
        provider = Provider('deepseek', 'deepseek-chat', is_local=False)
        print("   ✅ DeepSeek provider initialized successfully")
    except Exception as e:
        print(f"   ❌ Provider initialization failed: {e}")
        return False

    # Test 2: Simple API call
    print("2. Testing DeepSeek API call...")
    try:
        # Create a simple memory structure for testing
        messages = [{"role": "user", "content": "Write a simple Python function to calculate factorial of a number. Just show the code, no explanation."}]
        response = provider.respond(messages, verbose=False)
        if response and len(response) > 10:
            print("   ✅ API call successful")
            print(f"   📝 Response preview: {response[:100]}...")
        else:
            print("   ⚠️  No response received or response too short")
            return False
    except Exception as e:
        print(f"   ❌ API call failed: {e}")
        return False

    # Test 3: Check if essential services are running
    print("3. Testing Docker services...")
    try:
        import requests
        # Test SearxNG
        searxng_response = requests.get("http://localhost:8080", timeout=5)
        if searxng_response.status_code == 200:
            print("   ✅ SearxNG service is running")
        else:
            print("   ⚠️  SearxNG service not responding properly")
    except Exception as e:
        print(f"   ⚠️  SearxNG service check failed: {e}")

    print("\n🎉 Basic functionality test completed!")
    return True

if __name__ == "__main__":
    print("AgenticSeek Test Suite")
    print("=" * 50)
    
    # Run the test
    success = asyncio.run(test_basic_functionality())
    
    if success:
        print("\n✅ All tests passed! AgenticSeek is ready for data science work.")
        print("\nNext steps:")
        print("- Try running: python cli.py")
        print("- Test with data science queries")
        print("- Explore web search capabilities")
    else:
        print("\n❌ Some tests failed. Please check the setup.")
    
    sys.exit(0 if success else 1)
